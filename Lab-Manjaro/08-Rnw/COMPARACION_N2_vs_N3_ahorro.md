# Comparación: Nivel 2 vs Nivel 3 - Ejercicios de Ahorro ICFES

## 📊 Análisis Comparativo Detallado

### 🎯 **NIVEL 2: ahorro_interpretacion_representacion_n2_v2.Rnw**

#### Características Cognitivas:
- **Lectura directa** de información tabular
- **Cálculos aritméticos simples** (sumas, porcentajes básicos)
- **Comparación numérica directa** entre totales
- **Interpretación literal** de datos presentados
- **Decisión basada en criterio único** (mayor total)

#### Estructura del Problema:
```
Contexto: 4 estudiantes ahorran durante 4 meses
Datos: Tabla simple con montos mensuales
Pregunta: "¿Quién ahorró más dinero?"
Respuesta: Comparación directa de totales
```

#### Demanda Cognitiva:
- ✅ Sumar columnas
- ✅ Comparar 4 números
- ✅ Identificar el mayor
- ❌ No requiere análisis de tendencias
- ❌ No requiere selección de métodos
- ❌ No requiere interpretación de representaciones

---

### 🎯 **NIVEL 3: ahorro_interpretacion_representacion_n3_v1.Rnw**

#### Características Cognitivas:
- **Síntesis de múltiples fuentes** (tabla + estadísticas calculadas)
- **Análisis de eficiencia relativa** (no solo totales absolutos)
- **Selección de representación óptima** para análisis específico
- **Evaluación de métodos analíticos** diferentes
- **Justificación de selección** basada en propósito analítico

#### Estructura del Problema:
```
Contexto: 3 estudiantes con estrategias diferentes durante 6 meses
Datos: Tabla + tasas de crecimiento + totales + análisis temporal
Pregunta: "¿Cuál representación es MÁS ADECUADA para analizar eficiencia relativa?"
Respuesta: Evaluación de 4 tipos de representaciones gráficas
```

#### Demanda Cognitiva:
- ✅ Interpretar múltiples fuentes de información
- ✅ Comprender conceptos de eficiencia relativa
- ✅ Evaluar ventajas/desventajas de representaciones
- ✅ Aplicar razonamiento proporcional
- ✅ Justificar selección metodológica

---

## 🔍 **DIFERENCIAS CLAVE POR DIMENSIÓN**

### 1. **Complejidad de Datos**
| Aspecto | Nivel 2 | Nivel 3 |
|---------|---------|---------|
| **Fuentes** | 1 tabla simple | Tabla + estadísticas calculadas |
| **Variables** | Monto mensual | Monto + tasas + eficiencia |
| **Período** | 4 meses | 6 meses |
| **Estudiantes** | 4 | 3 (análisis más profundo) |

### 2. **Tipo de Pregunta**
| Aspecto | Nivel 2 | Nivel 3 |
|---------|---------|---------|
| **Enfoque** | "¿Quién ahorró más?" | "¿Cuál representación es mejor?" |
| **Criterio** | Valor absoluto | Método analítico |
| **Respuesta** | Identificación directa | Evaluación metodológica |

### 3. **Habilidades Matemáticas**
| Habilidad | Nivel 2 | Nivel 3 |
|-----------|---------|---------|
| **Aritmética básica** | ✅ Principal | ✅ Secundaria |
| **Análisis proporcional** | ❌ | ✅ Principal |
| **Interpretación gráfica** | ❌ | ✅ Principal |
| **Selección metodológica** | ❌ | ✅ Principal |

### 4. **Competencias ICFES**
| Competencia | Nivel 2 | Nivel 3 |
|-------------|---------|---------|
| **Interpretación** | Literal | Analítica |
| **Representación** | Lectura | Selección/Evaluación |
| **Razonamiento** | Directo | Comparativo/Justificado |

---

## 🧠 **ANÁLISIS COGNITIVO DETALLADO**

### **Nivel 2: Procesamiento Directo**
```
Datos → Cálculo Simple → Comparación → Respuesta
  ↓         ↓             ↓           ↓
Tabla → Suma columnas → Mayor total → Estudiante X
```

**Tiempo estimado:** 2-3 minutos  
**Errores comunes:** Errores aritméticos, lectura incorrecta de tabla

### **Nivel 3: Procesamiento Analítico**
```
Múltiples Fuentes → Síntesis → Evaluación Metodológica → Justificación → Respuesta
       ↓              ↓              ↓                    ↓            ↓
Tabla+Stats → Comprensión → Análisis de opciones → Argumentación → Método X
```

**Tiempo estimado:** 5-7 minutos  
**Errores comunes:** Confundir eficiencia con totales, no justificar selección

---

## 📈 **PROGRESIÓN PEDAGÓGICA**

### **Secuencia de Aprendizaje:**

1. **Nivel 2 (Fundacional):**
   - Establece comprensión básica de tablas
   - Desarrolla habilidades aritméticas
   - Introduce contexto de ahorro

2. **Nivel 3 (Analítico):**
   - Construye sobre Nivel 2
   - Introduce análisis comparativo
   - Desarrolla pensamiento metodológico
   - Prepara para Nivel 4

3. **Nivel 4 (Proyectado):**
   - Construcción de representaciones
   - Crítica de métodos existentes
   - Análisis multivariable complejo

---

## 🎯 **OBJETIVOS DIFERENCIADOS**

### **Nivel 2 - Objetivos:**
- ✅ Leer e interpretar tablas básicas
- ✅ Realizar cálculos aritméticos simples
- ✅ Comparar valores numéricos
- ✅ Tomar decisiones basadas en criterios únicos

### **Nivel 3 - Objetivos:**
- ✅ Sintetizar información de múltiples fuentes
- ✅ Comprender conceptos de eficiencia relativa
- ✅ Evaluar métodos de representación
- ✅ Justificar selecciones metodológicas
- ✅ Aplicar razonamiento proporcional

---

## 🔧 **IMPLEMENTACIÓN TÉCNICA**

### **Nivel 2:**
```r
# Estructura simple
datos_simples <- data.frame(mes1, mes2, mes3, mes4)
total <- rowSums(datos_simples)
respuesta <- which.max(total)
```

### **Nivel 3:**
```r
# Estructura compleja
datos_complejos <- data.frame(6_meses_x_3_estudiantes)
tasas_crecimiento <- calcular_tasas(datos)
eficiencia_relativa <- calcular_eficiencia(datos)
opciones_representacion <- evaluar_metodos()
respuesta <- seleccionar_optima(opciones)
```

---

## 📋 **CRITERIOS DE EVALUACIÓN**

### **Nivel 2:**
- ✅ Precisión aritmética
- ✅ Lectura correcta de tabla
- ✅ Identificación del mayor valor

### **Nivel 3:**
- ✅ Comprensión de eficiencia relativa
- ✅ Evaluación de representaciones
- ✅ Justificación de selección
- ✅ Aplicación de razonamiento proporcional

---

## 🎓 **CONCLUSIONES PEDAGÓGICAS**

### **Progresión Natural:**
El Nivel 3 construye orgánicamente sobre el Nivel 2, manteniendo el contexto familiar del ahorro pero elevando significativamente la demanda cognitiva.

### **Diferenciación Clara:**
- **N2**: "¿Qué dice la información?"
- **N3**: "¿Cómo analizar mejor la información?"

### **Preparación para N4:**
El Nivel 3 prepara para el Nivel 4 al introducir evaluación metodológica y justificación analítica.

---

**Análisis realizado por:** Agente Augment  
**Fecha:** 2025-01-16  
**Basado en:** Marco de Referencia ICFES Matemáticas
