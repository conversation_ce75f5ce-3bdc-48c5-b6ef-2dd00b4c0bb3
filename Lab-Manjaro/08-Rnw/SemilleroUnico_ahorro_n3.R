# Carga de la librería r-exams
library(exams)

# Configurar modo generación de exámenes para evitar pruebas test_that
.exams_generation_mode <- TRUE

# Definición del archivo de examen y configuración inicial
archivo_examen <- "ahorro_interpretacion_representacion_n3_v1.Rnw"
copias <- 1
numpreg <- 1
semilla <- sample(100:1e8, 1)
set.seed(semilla)
dir_salida <- "salida"
dir_ejercicios <- "."  # Directorio actual

# Nombre del archivo sin la extensión .Rnw
nombre_sin_extension <- sub("\\.Rnw$", "", archivo_examen)
nombre_arch <- paste0(nombre_sin_extension, "_")

################################################################################
# Creación del examen en formato HTML

cat("🚀 Generando ejercicio nivel 3 en HTML...\n")
cat("📁 Archivo:", archivo_examen, "\n")
cat("🎲 Semilla:", semilla, "\n")

tryCatch({
  exams2html(archivo_examen,
             svg = FALSE,
             verbose = TRUE,
             template = "plain",
             encoding = "UTF-8",
             name = paste0(nombre_sin_extension, "_test"),
             dir = dir_salida,
             edir = dir_ejercicios)
  
  cat("✅ Ejercicio nivel 3 generado exitosamente en HTML\n")
  cat("📂 Ubicación: ./salida/", nombre_sin_extension, "_test1.html\n", sep="")
  
}, error = function(e) {
  cat("❌ Error al generar HTML:", e$message, "\n")
  print(e)
})

################################################################################
# Generación para PDF

cat("\n🚀 Generando ejercicio nivel 3 en PDF...\n")

tryCatch({
  exams2pdf(archivo_examen,
            n = 1,
            name = paste0(nombre_sin_extension, "_test"),
            encoding = "UTF-8",
            template = "plain",
            dir = dir_salida,
            edir = dir_ejercicios,
            verbose = TRUE)
  
  cat("✅ Ejercicio nivel 3 generado exitosamente en PDF\n")
  cat("📂 Ubicación: ./salida/", nombre_sin_extension, "_test1.pdf\n", sep="")
  
}, error = function(e) {
  cat("❌ Error al generar PDF:", e$message, "\n")
  print(e)
})

################################################################################
# Crear el directorio de salida si no existe
if (!dir.exists(dir_salida)) {
  dir.create(dir_salida)
  cat("📁 Directorio de salida creado:", dir_salida, "\n")
}

cat("\n🎯 Proceso completado\n")
cat("🔍 Revisa los archivos generados en la carpeta 'salida'\n")
