\documentclass[12pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

\begin{document}
\begin{enumerate}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Configuración inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")
options(scipen = 999)
options(digits = 10)

# Librerías esenciales
library(exams)
library(digest)

# Configuración TikZ
typ <- match_exams_device()
if(match_exams_call() == "exams2nops") typ <- "tex"

# Función para formatear números enteros
formatear_entero <- function(x) {
  formatC(as.integer(round(x)), format='d', big.mark='')
}

# Semilla aleatoria para reproducibilidad
set.seed(sample(1:100000, 1))

# Generación de datos para nivel 3 - Análisis de estrategias de ahorro
# Tres estudiantes con diferentes patrones de ahorro durante 6 meses

# Estudiantes
estudiantes <- c("Ana", "Bruno", "Carla")

# Datos base para cada estudiante (en miles de pesos)
# Ana: Crecimiento constante
ana_base <- c(50, 65, 80, 95, 110, 125)

# Bruno: Crecimiento acelerado inicial, luego estable
bruno_base <- c(30, 55, 75, 85, 90, 95)

# Carla: Crecimiento irregular pero con tendencia positiva
carla_base <- c(40, 45, 70, 65, 85, 100)

# Crear matriz de datos
datos_ahorro <- data.frame(
  Mes = 1:6,
  Ana = ana_base,
  Bruno = bruno_base,
  Carla = carla_base
)

# Calcular totales y estadísticas para análisis nivel 3
total_ana <- sum(ana_base)
total_bruno <- sum(bruno_base)
total_carla <- sum(carla_base)

# Calcular tasas de crecimiento promedio mensual
tasa_ana <- ((ana_base[6] - ana_base[1]) / ana_base[1]) / 5 * 100
tasa_bruno <- ((bruno_base[6] - bruno_base[1]) / bruno_base[1]) / 5 * 100
tasa_carla <- ((carla_base[6] - carla_base[1]) / carla_base[1]) / 5 * 100

# Calcular eficiencia relativa (ahorro final / ahorro inicial)
eficiencia_ana <- ana_base[6] / ana_base[1]
eficiencia_bruno <- bruno_base[6] / bruno_base[1]
eficiencia_carla <- carla_base[6] / carla_base[1]

# Determinar cuál estudiante tiene mejor eficiencia relativa
mejor_eficiencia <- which.max(c(eficiencia_ana, eficiencia_bruno, eficiencia_carla))
estudiante_mejor <- estudiantes[mejor_eficiencia]

# Crear opciones de respuesta para análisis nivel 3
# La pregunta será sobre cuál representación es más adecuada para mostrar eficiencia relativa

opciones <- c(
  "Una grafica de barras que muestre los totales acumulados de cada estudiante",
  "Una grafica de lineas que muestre la evolucion mensual del ahorro de cada estudiante",
  "Una grafica de barras que muestre las tasas de crecimiento porcentual de cada estudiante",
  "Una tabla que muestre unicamente los valores del primer y ultimo mes de cada estudiante"
)

# La respuesta correcta es la opcion C (tasas de crecimiento porcentual)
# porque para analizar eficiencia relativa, las tasas de crecimiento son mas informativas
respuesta_correcta <- 3
soluciones <- c(FALSE, FALSE, TRUE, FALSE)

# Crear datos con error deliberado para una de las representaciones
# (caracteristica nivel 3: detectar inconsistencias)
datos_con_error <- datos_ahorro
datos_con_error$Bruno[4] <- 95  # Error: deberia ser 85
@

\begin{question}

Tres estudiantes implementaron diferentes estrategias de ahorro durante 6 meses. La siguiente tabla muestra el dinero ahorrado mensualmente por cada uno (en miles de pesos):

<<echo=FALSE, results=tex>>=
# Crear tabla manualmente para evitar problemas de encoding
cat("\\begin{table}[H]\n")
cat("\\centering\n")
cat("\\caption{Ahorro mensual por estudiante (miles de pesos)}\n")
cat("\\begin{tabular}{|c|c|c|c|}\n")
cat("\\hline\n")
cat("Mes & Ana & Bruno & Carla \\\\\n")
cat("\\hline\n")
for(i in 1:6) {
  cat(i, "&", datos_ahorro$Ana[i], "&", datos_ahorro$Bruno[i], "&", datos_ahorro$Carla[i], "\\\\\n")
}
cat("\\hline\n")
cat("\\end{tabular}\n")
cat("\\end{table}\n")
@

Adicionalmente, se presenta la siguiente información calculada:

\begin{itemize}
\item Ahorro total de Ana: \Sexpr{formatear_entero(total_ana)} mil pesos
\item Ahorro total de Bruno: \Sexpr{formatear_entero(total_bruno)} mil pesos
\item Ahorro total de Carla: \Sexpr{formatear_entero(total_carla)} mil pesos
\item Tasa de crecimiento promedio mensual de Ana: \Sexpr{round(tasa_ana, 1)}\%
\item Tasa de crecimiento promedio mensual de Bruno: \Sexpr{round(tasa_bruno, 1)}\%
\item Tasa de crecimiento promedio mensual de Carla: \Sexpr{round(tasa_carla, 1)}\%
\end{itemize}

Un analista financiero necesita determinar cual estudiante tuvo la estrategia de ahorro mas eficiente en terminos de crecimiento relativo. Para esto, debe seleccionar la representacion grafica mas adecuada que permita comparar la eficiencia relativa de las tres estrategias.

¿Cual de las siguientes representaciones es la MAS ADECUADA para analizar y comparar la eficiencia relativa de las estrategias de ahorro?

<<echo=FALSE, results=tex>>=
answerlist(opciones)
@

\end{question}

\begin{solution}

Para analizar la eficiencia relativa de las estrategias de ahorro, es necesario considerar no solo los montos totales, sino cómo cada estudiante logró hacer crecer su ahorro en relación con su punto de partida.

Análisis de cada opción:

\begin{itemize}
\item \textbf{Opción A}: Una gráfica de barras con totales acumulados muestra el resultado final, pero no permite evaluar la eficiencia relativa considerando los diferentes puntos de partida.

\item \textbf{Opción B}: Una gráfica de líneas muestra la evolución temporal, lo cual es útil para ver tendencias, pero no facilita la comparación directa de eficiencia relativa entre estudiantes con diferentes montos iniciales.

\item \textbf{Opción C (CORRECTA)}: Una gráfica de barras con tasas de crecimiento porcentual es la más adecuada porque:
  \begin{itemize}
  \item Normaliza las diferencias en los montos iniciales
  \item Permite comparar directamente la eficiencia relativa
  \item Muestra qué tan efectiva fue cada estrategia independientemente del capital inicial
  \item Facilita identificar cuál estudiante logró el mayor crecimiento proporcional
  \end{itemize}

\item \textbf{Opción D}: Una tabla con solo valores inicial y final proporciona información limitada y no facilita la visualización comparativa de la eficiencia.
\end{itemize}

Basándose en las tasas de crecimiento calculadas:
\begin{itemize}
\item Ana: \Sexpr{round(tasa_ana, 1)}\% mensual
\item Bruno: \Sexpr{round(tasa_bruno, 1)}\% mensual
\item Carla: \Sexpr{round(tasa_carla, 1)}\% mensual
\end{itemize}

\Sexpr{estudiante_mejor} tuvo la estrategia más eficiente en términos de crecimiento relativo.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(soluciones, "Verdadero", "Falso"))
@

\end{solution}

%% META-INFORMATION
\exname{Ahorro Interpretacion Representacion N3 V1}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(soluciones)}}
\exshuffle{TRUE}
\exsection{Interpretacion y Representacion}

\end{document}
