\documentclass[12pt,a4paper]{article}

%% paquetes básicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{tikz,xcolor}
\usepackage{enumitem}

%% bibliotecas TikZ
\usetikzlibrary{automata,positioning,calc,arrows}

%% entornos para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Configuracion para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA
options(scipen = 999)  # Evitar notacion cientifica completamente
options(digits = 10)   # Suficientes digitos para numeros grandes

# Librerias esenciales
library(exams)

# Funcion para formatear numeros enteros
formatear_entero <- function(x) {
  formatC(as.integer(round(x)), format='d', big.mark='')
}

# Semilla aleatoria para reproducibilidad
#set.seed(sample(1:100000, 1))

# Generación de datos para nivel 3 - Análisis de estrategias de ahorro
# Tres estudiantes con diferentes patrones de ahorro durante 6 meses

# Estudiantes
estudiantes <- c("Ana", "Bruno", "Carla")

# Datos base para cada estudiante (en miles de pesos)
# Ana: Crecimiento constante
ana_base <- c(50, 65, 80, 95, 110, 125)

# Bruno: Crecimiento acelerado inicial, luego estable
bruno_base <- c(30, 55, 75, 85, 90, 95)

# Carla: Crecimiento irregular pero con tendencia positiva
carla_base <- c(40, 45, 70, 65, 85, 100)

# Crear matriz de datos
datos_ahorro <- data.frame(
  Mes = 1:6,
  Ana = ana_base,
  Bruno = bruno_base,
  Carla = carla_base
)

# Calcular totales y estadísticas para análisis nivel 3
total_ana <- sum(ana_base)
total_bruno <- sum(bruno_base)
total_carla <- sum(carla_base)

# Calcular tasas de crecimiento promedio mensual
tasa_ana <- ((ana_base[6] - ana_base[1]) / ana_base[1]) / 5 * 100
tasa_bruno <- ((bruno_base[6] - bruno_base[1]) / bruno_base[1]) / 5 * 100
tasa_carla <- ((carla_base[6] - carla_base[1]) / carla_base[1]) / 5 * 100

# Calcular eficiencia relativa (ahorro final / ahorro inicial)
eficiencia_ana <- ana_base[6] / ana_base[1]
eficiencia_bruno <- bruno_base[6] / bruno_base[1]
eficiencia_carla <- carla_base[6] / carla_base[1]

# Determinar cuál estudiante tiene mejor eficiencia relativa
mejor_eficiencia <- which.max(c(eficiencia_ana, eficiencia_bruno, eficiencia_carla))
estudiante_mejor <- estudiantes[mejor_eficiencia]

# Crear datos para las gráficas de opciones de respuesta
# Opción A: Totales acumulados
totales <- c(total_ana, total_bruno, total_carla)
names(totales) <- estudiantes

# Opción B: Evolución mensual (datos ya en datos_ahorro)

# Opción C: Tasas de crecimiento porcentual (CORRECTA)
tasas <- c(tasa_ana, tasa_bruno, tasa_carla)
names(tasas) <- estudiantes

# Opción D: Comparación inicial vs final
inicial_final <- data.frame(
  Estudiante = rep(estudiantes, 2),
  Periodo = rep(c("Inicial", "Final"), each = 3),
  Monto = c(ana_base[1], bruno_base[1], carla_base[1],
            ana_base[6], bruno_base[6], carla_base[6])
)

# Crear opciones de respuesta con argumentaciones
opciones <- c(
  "porque muestra claramente los montos totales que cada estudiante logró ahorrar",
  "porque permite visualizar las tendencias y patrones de ahorro a lo largo del tiempo",
  "porque normaliza las diferencias iniciales y permite comparar directamente la eficiencia relativa",
  "porque facilita la comparación directa entre el punto de partida y el resultado final"
)

# La respuesta correcta es la opcion C (tasas de crecimiento porcentual)
respuesta_correcta <- 3
soluciones <- c(FALSE, FALSE, TRUE, FALSE)

# Crear datos con error deliberado para una de las representaciones
# (caracteristica nivel 3: detectar inconsistencias)
datos_con_error <- datos_ahorro
datos_con_error$Bruno[4] <- 95  # Error: deberia ser 85
@

\begin{question}

Tres estudiantes implementaron diferentes estrategias de ahorro durante 6 meses. La siguiente tabla muestra el dinero ahorrado mensualmente por cada uno (en miles de pesos):

\par\vspace{0.5cm}

\begin{center}
\textbf{Ahorro mensual por estudiante (miles de pesos)}
\par\vspace{0.3cm}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{Mes} & \textbf{Ana} & \textbf{Bruno} & \textbf{Carla} \\
\hline
1 & \Sexpr{ana_base[1]} & \Sexpr{bruno_base[1]} & \Sexpr{carla_base[1]} \\
\hline
2 & \Sexpr{ana_base[2]} & \Sexpr{bruno_base[2]} & \Sexpr{carla_base[2]} \\
\hline
3 & \Sexpr{ana_base[3]} & \Sexpr{bruno_base[3]} & \Sexpr{carla_base[3]} \\
\hline
4 & \Sexpr{ana_base[4]} & \Sexpr{bruno_base[4]} & \Sexpr{carla_base[4]} \\
\hline
5 & \Sexpr{ana_base[5]} & \Sexpr{bruno_base[5]} & \Sexpr{carla_base[5]} \\
\hline
6 & \Sexpr{ana_base[6]} & \Sexpr{bruno_base[6]} & \Sexpr{carla_base[6]} \\
\hline
\end{tabular}
\end{center}

\par\vspace{0.5cm}

Adicionalmente, se presenta la siguiente información calculada:

\begin{itemize}
\item Ahorro total de Ana: \Sexpr{formatear_entero(total_ana)} mil pesos
\item Ahorro total de Bruno: \Sexpr{formatear_entero(total_bruno)} mil pesos
\item Ahorro total de Carla: \Sexpr{formatear_entero(total_carla)} mil pesos
\item Tasa de crecimiento promedio mensual de Ana: \Sexpr{round(tasa_ana, 1)}\%
\item Tasa de crecimiento promedio mensual de Bruno: \Sexpr{round(tasa_bruno, 1)}\%
\item Tasa de crecimiento promedio mensual de Carla: \Sexpr{round(tasa_carla, 1)}\%
\end{itemize}

\par\vspace{0.5cm}

Un analista financiero necesita determinar cual estudiante tuvo la estrategia de ahorro mas eficiente en terminos de crecimiento relativo. Para esto, debe seleccionar la representacion grafica mas adecuada que permita comparar la eficiencia relativa de las tres estrategias.

\par\vspace{0.5cm}

¿Cual de las siguientes graficas representa MEJOR la eficiencia relativa de las estrategias de ahorro?

\par\vspace{0.5cm}

<<echo=FALSE, results=tex>>=
# Generar las 4 gráficas como opciones de respuesta

# Opción A: Gráfica de barras con totales acumulados
cat("\\textbf{A.} ")
@

<<fig=TRUE, height=3, width=4, echo=FALSE, eps=FALSE, results=hide>>=
par(mar = c(4, 4, 2, 1))
barplot(totales,
        main = "Totales Acumulados por Estudiante",
        ylab = "Miles de pesos",
        xlab = "Estudiantes",
        col = c("lightblue", "lightgreen", "lightcoral"),
        ylim = c(0, max(totales) * 1.1))
text(x = 1:3, y = totales + max(totales)*0.05, labels = totales, cex = 0.8)
@

<<echo=FALSE, results=tex>>=
cat("\\textbf{", opciones[1], "}\n\n")
cat("\\par\\vspace{0.3cm}\n\n")

# Opción B: Gráfica de líneas con evolución mensual
cat("\\textbf{B.} ")
@

<<fig=TRUE, height=3, width=4, echo=FALSE, eps=FALSE, results=hide>>=
par(mar = c(4, 4, 2, 1))
plot(1:6, ana_base, type = "l", col = "blue", lwd = 2,
     main = "Evolución Mensual del Ahorro",
     xlab = "Mes", ylab = "Miles de pesos",
     ylim = c(min(c(ana_base, bruno_base, carla_base)) * 0.9,
              max(c(ana_base, bruno_base, carla_base)) * 1.1))
lines(1:6, bruno_base, col = "green", lwd = 2)
lines(1:6, carla_base, col = "red", lwd = 2)
legend("topleft", legend = estudiantes,
       col = c("blue", "green", "red"), lwd = 2, cex = 0.8)
@

<<echo=FALSE, results=tex>>=
cat("\\textbf{", opciones[2], "}\n\n")
cat("\\par\\vspace{0.3cm}\n\n")

# Opción C: Gráfica de barras con tasas de crecimiento (CORRECTA)
cat("\\textbf{C.} ")
@

<<fig=TRUE, height=3, width=4, echo=FALSE, eps=FALSE, results=hide>>=
par(mar = c(4, 4, 2, 1))
barplot(tasas,
        main = "Tasas de Crecimiento Promedio Mensual",
        ylab = "Porcentaje (%)",
        xlab = "Estudiantes",
        col = c("lightblue", "lightgreen", "lightcoral"),
        ylim = c(0, max(tasas) * 1.2))
text(x = 1:3, y = tasas + max(tasas)*0.05,
     labels = paste0(round(tasas, 1), "%"), cex = 0.8)
@

<<echo=FALSE, results=tex>>=
cat("\\textbf{", opciones[3], "}\n\n")
cat("\\par\\vspace{0.3cm}\n\n")

# Opción D: Gráfica de barras inicial vs final
cat("\\textbf{D.} ")
@

<<fig=TRUE, height=3, width=4, echo=FALSE, eps=FALSE, results=hide>>=
par(mar = c(4, 4, 2, 1))
library(graphics)
inicial <- c(ana_base[1], bruno_base[1], carla_base[1])
final <- c(ana_base[6], bruno_base[6], carla_base[6])
datos_comparacion <- rbind(inicial, final)
colnames(datos_comparacion) <- estudiantes

barplot(datos_comparacion,
        main = "Comparación Inicial vs Final",
        ylab = "Miles de pesos",
        xlab = "Estudiantes",
        col = c("lightgray", "darkgray"),
        beside = TRUE,
        legend.text = c("Inicial", "Final"),
        args.legend = list(x = "topleft", cex = 0.8))
@

<<echo=FALSE, results=tex>>=
cat("\\textbf{", opciones[4], "}\n\n")
@

\end{question}

\begin{solution}

Para analizar la eficiencia relativa de las estrategias de ahorro, es necesario considerar no solo los montos totales, sino cómo cada estudiante logró hacer crecer su ahorro en relación con su punto de partida.

\par\vspace{0.5cm}

Análisis de cada gráfica presentada:

\begin{itemize}
\item \textbf{Gráfica A (Totales Acumulados)}: Muestra claramente los montos totales que cada estudiante logró ahorrar, pero no permite evaluar la eficiencia relativa porque no considera las diferencias en los puntos de partida. Bruno aparenta menor rendimiento cuando en realidad fue muy eficiente.

\item \textbf{Gráfica B (Evolución Mensual)}: Permite visualizar las tendencias y patrones de ahorro a lo largo del tiempo, lo cual es útil para identificar comportamientos, pero requiere análisis adicional para determinar eficiencia relativa ya que las escalas iniciales son diferentes.

\item \textbf{Gráfica C (Tasas de Crecimiento - CORRECTA)}: Normaliza las diferencias iniciales y permite comparar directamente la eficiencia relativa porque:
  \begin{itemize}
  \item Muestra el crecimiento porcentual promedio mensual de cada estudiante
  \item Elimina el sesgo de los montos iniciales diferentes
  \item Permite identificar claramente cuál estrategia fue más efectiva proporcionalmente
  \item Facilita la comparación objetiva de eficiencia independiente del capital inicial
  \end{itemize}

\item \textbf{Gráfica D (Inicial vs Final)}: Facilita la comparación directa entre el punto de partida y el resultado final, pero no proporciona información sobre la eficiencia relativa ya que no normaliza las diferencias en los montos iniciales.
\end{itemize}

\par\vspace{0.5cm}

Basándose en las tasas de crecimiento calculadas:
\begin{itemize}
\item Ana: \Sexpr{round(tasa_ana, 1)}\% mensual
\item Bruno: \Sexpr{round(tasa_bruno, 1)}\% mensual
\item Carla: \Sexpr{round(tasa_carla, 1)}\% mensual
\end{itemize}

\par\vspace{0.5cm}

\Sexpr{estudiante_mejor} tuvo la estrategia más eficiente en términos de crecimiento relativo.

<<echo=FALSE, results=tex>>=
answerlist(ifelse(soluciones, "Verdadero", "Falso"))
@

\end{solution}

%% META-INFORMATION
%% \extype{schoice}
%% \exsolution{\Sexpr{mchoice2string(soluciones)}}
%% \exname{Ahorro Interpretacion Representacion N3 V1}

\end{document}
